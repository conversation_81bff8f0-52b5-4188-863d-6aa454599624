'use client';

import { Button } from '@telegram-apps/telegram-ui';

import { TonLogo } from '@/components/TonLogo';
import type { OrderEntity } from '@/constants/core.constants';

interface SellButtonComponentProps {
  order: OrderEntity;
  label?: string;
  className?: string;
  tonLogoClassName?: string;
}

export function SellButtonComponent({
  order,
  label = 'Buy',
  className = '',
  tonLogoClassName = '',
}: SellButtonComponentProps) {
  const hasSecondaryPrice =
    order.secondaryMarketPrice && order.secondaryMarketPrice > 0;
  const currentPrice = hasSecondaryPrice
    ? order.secondaryMarketPrice
    : order.price;

  return (
    <Button
      className={`w-full [&>h6]:flex [&>h6]:items-center [&>h6]:justify-center [&>h6]:gap-1 ${className}`}
    >
      <div className="flex items-center gap-1">
        <span>{label}</span>
        <span className="text-lg font-bold">{currentPrice}</span>
        {hasSecondaryPrice && (
          <span className="text-sm line-through ml-1">({order.price})</span>
        )}
        <TonLogo size={24} className={`-ml-1 ${tonLogoClassName}`} />
      </div>
    </Button>
  );
}
