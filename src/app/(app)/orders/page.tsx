'use client';

import { Loader2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import { getUserOrders } from '@/api/order-api';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { VirtualizedCard } from '@/components/ui/virtualized-card';
import { ItemCacheProvider } from '@/components/ui/virtualized-grid';
import type { OrderEntity } from '@/constants/core.constants';
import { UserType } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';

import {
  MyOrdersTabs,
  type MyOrdersTabType,
} from './components/my-orders-tabs';
import { UserOrderDetailsDrawer } from './user-order-details-drawer';

export default function OrdersPage() {
  const { currentUser } = useRootContext();
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [activeTab, setActiveTab] = useState<MyOrdersTabType>('buy');

  const fetchUserOrders = useCallback(async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    try {
      const userOrders = await getUserOrders(currentUser.id);
      setOrders(userOrders);
    } catch (error) {
      console.error('Error fetching user orders:', error);
    } finally {
      setLoading(false);
    }
  }, [currentUser?.id]);

  useEffect(() => {
    fetchUserOrders();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderUpdate = () => {
    fetchUserOrders();
  };

  const getUserRole = (order: OrderEntity) => {
    return order.sellerId === currentUser?.id
      ? UserType.SELLER
      : UserType.BUYER;
  };

  const buyOrders = orders.filter(
    (order) => getUserRole(order) === UserType.BUYER,
  );
  const sellOrders = orders.filter(
    (order) => getUserRole(order) === UserType.SELLER,
  );

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <p className="text-[#708499] text-lg">You are not logged in</p>
          <p className="text-[#708499] text-sm">
            Click on login Telegram button to see your orders
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 pb-[75px]">
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-[#6ab2f2]" />
        </div>
      ) : orders.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-[#708499]">No orders found</p>
        </div>
      ) : (
        <Tabs value={activeTab}>
          <MyOrdersTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
            buyOrdersCount={buyOrders.length}
            sellOrdersCount={sellOrders.length}
          />

          <TabsContent value="buy" className="space-y-4">
            {buyOrders.length > 0 ? (
              <ItemCacheProvider>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {buyOrders.map((order, index) => (
                    <VirtualizedCard
                      variant="user-order"
                      key={order.id}
                      order={order}
                      userRole={UserType.BUYER}
                      onClick={() => handleOrderClick(order)}
                      index={index}
                      initialRenderedCount={8}
                    />
                  ))}
                </div>
              </ItemCacheProvider>
            ) : (
              <div className="text-center py-8">
                <p className="text-[#708499]">No buy orders found</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="sell" className="space-y-4">
            {sellOrders.length > 0 ? (
              <ItemCacheProvider>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {sellOrders.map((order, index) => (
                    <VirtualizedCard
                      variant="user-order"
                      key={order.id}
                      order={order}
                      userRole={UserType.SELLER}
                      onClick={() => handleOrderClick(order)}
                      index={index}
                      initialRenderedCount={8}
                    />
                  ))}
                </div>
              </ItemCacheProvider>
            ) : (
              <div className="text-center py-8">
                <p className="text-[#708499]">No sell orders found</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}

      <UserOrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={selectedOrder ? getUserRole(selectedOrder) : UserType.BUYER}
        onOrderUpdate={handleOrderUpdate}
      />
    </div>
  );
}
