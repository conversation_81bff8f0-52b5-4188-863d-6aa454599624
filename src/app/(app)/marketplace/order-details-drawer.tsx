'use client';

import { useState } from 'react';

import {
  getUserIdToFetch,
  getUserLabel,
  useOrderUserInfo,
} from '@/app/(app)/marketplace/use-order-user-info';
import { SellPriceDetails } from '@/components/shared/sell-price-details';
import { TonLogo } from '@/components/TonLogo';
import { ResellTxHistory } from '@/components/ui/order/resell-tx-history';
import { type OrderEntity, UserType } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';
import { executeMarketplaceOrderAction } from '@/utils/order-action-utils';
import { isSecondaryMarketOrder } from '@/utils/secondary-market-utils';

import { UserOrderPaymentDetailsSection } from '../orders/user-order-details-drawer/user-order-payment-details-section';
import {
  OrderDetailsActionButtons,
  OrderDetailsBaseDrawer,
  OrderDetailsDescriptionSection,
  OrderDetailsHeaderSection,
  OrderDetailsImageSection,
  OrderDetailsUserInfoSection,
} from './order-details-drawer/index';

interface OrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userType?: UserType;
  onOrderAction?: () => void;
}

export function OrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userType,
  onOrderAction,
}: OrderDetailsDrawerProps) {
  const { collections } = useRootContext();
  const [actionLoading, setActionLoading] = useState(false);
  const [showResellHistory, setShowResellHistory] = useState(false);

  const collection = order
    ? collections.find((c) => c.id === order.collectionId) || null
    : null;

  const isSecondary = order ? isSecondaryMarketOrder(order) : false;

  const effectiveUserType = userType || UserType.BUYER;

  const userIdToFetch = getUserIdToFetch(
    order?.buyerId,
    order?.sellerId,
    effectiveUserType,
  );
  const { userInfo, loading, handleClose } = useOrderUserInfo({
    userId: userIdToFetch,
    isOpen: open,
  });

  const handleAction = async () => {
    if (!order?.id) return;

    setActionLoading(true);

    let result;
    if (isSecondary) {
      // For secondary market orders, use the secondary market purchase function
      const { makeSecondaryMarketPurchase } = await import('@/api/orders-api');
      try {
        result = await makeSecondaryMarketPurchase(order.id);
      } catch (error) {
        console.error('Error making secondary market purchase:', error);
        result = { success: false };
      }
    } else {
      // For regular orders, use the existing marketplace action
      result = await executeMarketplaceOrderAction(order.id, effectiveUserType);
    }

    if (result.success) {
      onOpenChange(false);
      if (onOrderAction) {
        onOrderAction();
      }
    }
    setActionLoading(false);
  };

  const handleDrawerClose = () => {
    handleClose();
    onOpenChange(false);
  };

  const handleShowResellHistory = () => {
    setShowResellHistory(true);
  };

  const shouldShowResellHistory =
    order?.secondaryMarketPrice && order.secondaryMarketPrice > 0;

  if (!order) return null;

  const actionLabel = (
    <>
      {isSecondary ? (
        <div className="flex items-center gap-1">
          <span>
            Buy{' '}
            <span className="text-lg font-bold">
              {order.secondaryMarketPrice}
            </span>
          </span>
          <span className="-ml-1 translate-x-[2px] text-md line-through">
            &#40;{order.price}&#41;
          </span>
          <TonLogo className="-ml-[4px]" size={24} />
        </div>
      ) : (
        <div className="flex items-center gap-1">
          <span>
            {effectiveUserType === UserType.BUYER ? 'Buy' : 'Fulfill'}{' '}
            <span className="text-lg font-bold">{order.price}</span>
          </span>
          <TonLogo className="-ml-[5px]" size={24} />
        </div>
      )}
    </>
  );

  return (
    <OrderDetailsBaseDrawer open={open} onOpenChange={onOpenChange}>
      <OrderDetailsImageSection
        collectionId={order.collectionId}
        collection={collection}
      />

      <OrderDetailsHeaderSection
        {...{
          order,
          collection,
        }}
      />

      <SellPriceDetails order={order} className="py-4" />

      <div className="space-y-4">
        <OrderDetailsDescriptionSection collection={collection} />
      </div>

      <UserOrderPaymentDetailsSection order={order} />

      {userIdToFetch && (
        <OrderDetailsUserInfoSection
          {...{
            userInfo,
            loading,
          }}
          userLabel={isSecondary ? 'Reseller' : getUserLabel(effectiveUserType)}
        />
      )}

      <OrderDetailsActionButtons
        primaryAction={{
          label: actionLabel,
          onClick: handleAction,
          loading: actionLoading,
        }}
        secondaryAction={
          shouldShowResellHistory
            ? {
                label: 'Show Resell History',
                onClick: handleShowResellHistory,
              }
            : undefined
        }
        onClose={handleDrawerClose}
        actionLoading={actionLoading}
      />

      {showResellHistory && order && (
        <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center p-4">
          <div className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <ResellTxHistory
              order={order}
              onClose={() => setShowResellHistory(false)}
            />
          </div>
        </div>
      )}
    </OrderDetailsBaseDrawer>
  );
}
