'use client';

import { BaseOrderCard } from '@/components/shared/base-order-card';
import { SecondaryMarketBadge } from '@/components/shared/secondary-market-badge';
import { SellButtonComponent } from '@/components/shared/sell-button-component';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { isSecondaryMarketOrder } from '@/utils/secondary-market-utils';

interface OrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
}

export function OrderCard({ order, collection, onClick }: OrderCardProps) {
  const isSecondary = isSecondaryMarketOrder(order);

  return (
    <BaseOrderCard
      imageBadge={isSecondary && <SecondaryMarketBadge className="mt-2 ml-2" />}
      order={order}
      collection={collection}
      onClick={onClick}
    >
      <SellButtonComponent order={order} />
    </BaseOrderCard>
  );
}
